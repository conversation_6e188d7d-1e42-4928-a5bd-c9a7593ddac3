const { pool } = require("../database");
const openRouterService = require("../services/openRouterService");
const openAIService = require("../services/openAIService");
const websocketService = require("../services/websocketService");

// Generate painting ideas (immediate response with background processing)
async function generatePaintings(req, res) {
  if (!req.user || !req.user.id) {
    console.error("User not authenticated properly");
    return res.status(401).json({ error: "Authentication required" });
  }

  const { titleId, quantity = 5 } = req.body;

  if (!titleId) {
    return res.status(400).json({ error: "Title ID is required" });
  }

  try {
    console.log(
      `Starting painting generation for user ${req.user.id}, title ${titleId}, quantity ${quantity}`
    );

    // Validate title exists and user has access
    const [titleRows] = await pool.execute(
      "SELECT id, title, instructions FROM titles WHERE id = ? AND user_id = ?",
      [titleId, req.user.id]
    );

    if (titleRows.length === 0) {
      return res.status(404).json({ error: "Title not found" });
    }

    const title = titleRows[0];

    // Return immediately - don't wait for processing
    res.status(200).json({
      message: `Started generating ${quantity} paintings`,
      titleId,
      quantity,
    });

    // Process everything in background - don't await this
    processGenerationInBackground(req.user.id, titleId, title, quantity);
  } catch (error) {
    console.error("Error in generatePaintings:", error);
    if (!res.headersSent) {
      res.status(500).json({ error: "Failed to start painting generation" });
    }
  }
}

// Background processing function
async function processGenerationInBackground(userId, titleId, title, quantity) {
  const MAX_PARALLEL = 5;

  try {
    console.log(
      `Background processing started for user ${userId}, title ${titleId}`
    );

    // Get references for this title
    const [refRows] = await pool.execute(
      "SELECT id, image_data FROM references2 WHERE title_id = ? OR (user_id = ? AND is_global = 1)",
      [titleId, userId]
    );

    const references = refRows.map((row) => ({
      id: row.id,
      image_data: row.image_data,
    }));

    // Get previous ideas for this title to avoid duplication
    const [prevIdeas] = await pool.execute(
      "SELECT id, summary FROM ideas WHERE title_id = ? ORDER BY created_at DESC",
      [titleId]
    );

    console.log(
      `Found ${references.length} references and ${prevIdeas.length} previous ideas`
    );

    // Create placeholder paintings first and emit generation started
    const placeholderPaintings = [];
    for (let i = 0; i < quantity; i++) {
      // Create placeholder painting entry
      const [result] = await pool.execute(
        "INSERT INTO paintings (title_id, idea_id, status, progress_stage, progress_percentage) VALUES (?, ?, ?, ?, ?)",
        [titleId, null, "pending", "pending", 0]
      );

      placeholderPaintings.push({
        paintingId: result.insertId,
        titleId,
        ideaId: null,
        status: "pending",
        stage: "pending",
        percentage: 0,
        summary: `Painting ${i + 1}`,
        timestamp: new Date().toISOString(),
      });
    }

    // Emit generation started event immediately
    websocketService.emitGenerationStarted(userId, {
      titleId,
      placeholders: placeholderPaintings,
      totalCount: quantity,
    });

    // Generate ideas and process them one by one
    const processedIdeas = [];
    for (let i = 0; i < quantity; i++) {
      try {
        const paintingId = placeholderPaintings[i].paintingId;

        // Update status to creating_prompt
        await websocketService.updatePaintingProgress(
          paintingId,
          userId,
          "creating_prompt",
          `Creating prompt ${i + 1}/${quantity}...`,
          25
        );

        // Generate idea
        const idea = await openRouterService.generateIdeas(
          titleId,
          title.title,
          title.instructions,
          [...prevIdeas, ...processedIdeas]
        );

        processedIdeas.push(idea);

        // Update painting with idea_id and progress
        await pool.execute(
          "UPDATE paintings SET idea_id = ?, progress_stage = ?, progress_percentage = ? WHERE id = ?",
          [idea.id, "creating_prompt", 100, paintingId]
        );

        await websocketService.updatePaintingProgress(
          paintingId,
          userId,
          "creating_prompt",
          `Prompt created for painting ${i + 1}`,
          100
        );

        // Start image generation for this idea immediately (don't wait)
        generateImageInBackground(userId, paintingId, idea, references);
      } catch (error) {
        console.error(`Error processing idea ${i + 1}:`, error);
        // Mark this painting as failed
        const paintingId = placeholderPaintings[i].paintingId;
        await websocketService.updatePaintingProgress(
          paintingId,
          userId,
          "failed",
          "Failed to create prompt",
          0,
          error.message
        );
      }
    }

    console.log(
      `Background processing completed for user ${userId}, title ${titleId}`
    );
  } catch (error) {
    console.error("Error in background processing:", error);
  }
}

// Generate image in background for a single painting
async function generateImageInBackground(userId, paintingId, idea, references) {
  try {
    await openAIService.generateImage(
      userId,
      idea.id,
      idea.fullPrompt,
      references
    );
  } catch (error) {
    console.error(`Error generating image for painting ${paintingId}:`, error);
    // Error handling is done in openAIService via WebSocket
  }
}

// Get status of all paintings for a title
async function getPaintings(req, res) {
  if (!req.user || !req.user.id) {
    console.error("User not authenticated properly");
    return res.status(401).json({ error: "Authentication required" });
  }

  const { titleId } = req.params;
  const functionStartTime = Date.now();
  let stepStartTime = Date.now();

  if (!titleId) {
    return res.status(400).json({ error: "Title ID is required" });
  }
  console.log(`[Title ID: ${titleId}] getPaintings started.`);

  try {
    const titleCheckParams = [titleId];
    if (titleCheckParams.some((p) => p === undefined)) {
      console.error("Attempted to execute query with undefined parameter:", {
        titleCheckParams,
      });
      return res.status(500).json({
        error: "Internal server error: Invalid query parameter detected",
      });
    }

    const [titleCheck] = await pool.execute(
      "SELECT id FROM titles WHERE id = ?",
      titleCheckParams
    );
    if (titleCheck.length === 0) {
      console.warn(
        `[Title ID: ${titleId}] Title not found during initial check.`
      );
      return res.status(404).json({ error: "Title not found" });
    }
    console.log(
      `[Title ID: ${titleId}] Title existence check completed in ${
        Date.now() - stepStartTime
      }ms.`
    );
    stepStartTime = Date.now();

    const paintingQuery = `
      SELECT t.id, t.title_id, t.idea_id, t.image_url, t.status, t.created_at, t.error_message,
             t.used_reference_ids,
             i.summary, i.full_prompt as fullPrompt,
             titles.title as title_text, 
             titles.instructions as title_instructions
      FROM paintings t
      JOIN ideas i ON t.idea_id = i.id
      JOIN titles ON t.title_id = titles.id
      WHERE t.title_id = ?
      ORDER BY t.created_at DESC
    `;

    const paintingParams = [titleId];
    if (paintingParams.some((p) => p === undefined)) {
      console.error("Attempted to execute query with undefined parameter:", {
        paintingParams,
      });
      return res.status(500).json({
        error: "Internal server error: Invalid query parameter detected",
      });
    }

    const [paintingRows] = await pool.execute(paintingQuery, paintingParams);
    console.log(
      `[Title ID: ${titleId}] Initial painting query fetched ${
        paintingRows ? paintingRows.length : 0
      } rows in ${Date.now() - stepStartTime}ms.`
    );
    stepStartTime = Date.now();

    if (!paintingRows || paintingRows.length === 0) {
      console.log(
        `[Title ID: ${titleId}] No paintings found. Total time: ${
          Date.now() - functionStartTime
        }ms.`
      );
      return res.status(200).json({ paintings: [], referenceDataMap: {} }); // Return empty map
    }

    const allReferenceIds = new Set();
    paintingRows.forEach((row) => {
      if (row.used_reference_ids) {
        try {
          const refIds = JSON.parse(row.used_reference_ids);
          if (refIds && Array.isArray(refIds)) {
            refIds.forEach((id) => {
              if (id != null) allReferenceIds.add(id);
            });
          }
        } catch (e) {
          console.error(
            `[Title ID: ${titleId}] Error parsing used_reference_ids for painting ${row.id} (value: '${row.used_reference_ids}'):`,
            e.message
          );
        }
      }
    });
    console.log(
      `[Title ID: ${titleId}] Collected ${
        allReferenceIds.size
      } unique reference IDs in ${Date.now() - stepStartTime}ms.`
    );
    stepStartTime = Date.now();

    let serverReferenceDataMap = {}; // Changed to object for JSON response
    const uniqueRefIdsArray = Array.from(allReferenceIds);

    if (uniqueRefIdsArray.length > 0) {
      try {
        const placeholders = uniqueRefIdsArray.map(() => "?").join(",");

        // Validate all parameters before executing query
        if (uniqueRefIdsArray.some((p) => p === undefined)) {
          console.error(
            "Attempted to execute query with undefined parameter in reference IDs:",
            { uniqueRefIdsArray }
          );
          // Continue without reference data rather than failing the entire request
        } else {
          const [actualRefDataRows] = await pool.execute(
            `SELECT id, image_data FROM references2 WHERE id IN (${placeholders})`,
            uniqueRefIdsArray
          );
          actualRefDataRows.forEach((refRow) => {
            serverReferenceDataMap[refRow.id] = refRow.image_data; // Populate object
          });
          console.log(
            `[Title ID: ${titleId}] Bulk fetched ${
              Object.keys(serverReferenceDataMap).length
            } reference data items in ${Date.now() - stepStartTime}ms.`
          );
        }
      } catch (refQueryError) {
        console.error(
          `[Title ID: ${titleId}] Error fetching bulk reference data:`,
          refQueryError
        );
        console.log(
          `[Title ID: ${titleId}] Proceeding without detailed reference images due to bulk fetch error. Time before error: ${
            Date.now() - stepStartTime
          }ms.`
        );
      }
    }
    stepStartTime = Date.now();

    const paintingsWithDetails = paintingRows.map((row) => {
      let usedRefIdsList = [];
      let referenceCount = 0;

      if (row.used_reference_ids) {
        try {
          const refIds = JSON.parse(row.used_reference_ids);
          if (refIds && Array.isArray(refIds) && refIds.length > 0) {
            usedRefIdsList = refIds.filter(
              (id) => id != null && serverReferenceDataMap.hasOwnProperty(id)
            );
            referenceCount = usedRefIdsList.length;
          }
        } catch (e) {
          /* Error already logged */
        }
      }

      const promptDetails = {
        summary: row.summary || "",
        title: row.title_text || "Unknown Title",
        instructions:
          row.title_instructions || "No custom instructions provided",
        referenceCount: referenceCount,
        referenceImages: usedRefIdsList, // Now an array of IDs
        fullPrompt: row.fullPrompt || "",
      };

      return {
        id: row.id,
        idea_id: row.idea_id,
        title_id: row.title_id,
        image_url: row.image_url || "",
        status: row.status || "unknown",
        created_at: row.created_at || new Date(),
        error_message: row.error_message || "",
        summary: row.summary || "",
        promptDetails: promptDetails,
      };
    });
    console.log(
      `[Title ID: ${titleId}] Mapped paintings to details in ${
        Date.now() - stepStartTime
      }ms.`
    );

    console.log(
      `[Title ID: ${titleId}] getPaintings completed successfully in ${
        Date.now() - functionStartTime
      }ms.`
    );
    res.status(200).json({
      paintings: paintingsWithDetails,
      referenceDataMap: serverReferenceDataMap,
    });
  } catch (error) {
    console.error(
      `[Title ID: ${titleId}] Critical error in getPaintings (total time: ${
        Date.now() - functionStartTime
      }ms):`,
      error
    );
    res
      .status(500)
      .json({ error: `Failed to get paintings: ${error.message}` });
  }
}

// Get painting updates for reconnection
async function getPaintingUpdates(req, res) {
  if (!req.user || !req.user.id) {
    console.error("User not authenticated properly");
    return res.status(401).json({ error: "Authentication required" });
  }

  const { titleId } = req.params;

  if (!titleId) {
    return res.status(400).json({ error: "Title ID is required" });
  }

  try {
    const updates = await websocketService.getPaintingUpdates(
      req.user.id,
      titleId
    );
    res.status(200).json({ updates });
  } catch (error) {
    console.error("Error getting painting updates:", error);
    res.status(500).json({ error: "Failed to get painting updates" });
  }
}

module.exports = {
  generatePaintings,
  getPaintings,
  getPaintingUpdates,
};
