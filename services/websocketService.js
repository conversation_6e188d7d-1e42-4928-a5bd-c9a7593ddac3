const { pool } = require("../database");

/**
 * WebSocket service for real-time painting generation updates
 */
class WebSocketService {
  constructor() {
    this.io = null;
    this.userSockets = null;
  }

  /**
   * Initialize the WebSocket service with io and userSockets from global
   */
  init() {
    this.io = global.io;
    this.userSockets = global.userSockets;
  }

  /**
   * Emit painting progress update to a specific user
   * @param {number} userId - User ID
   * @param {object} paintingUpdate - Painting update data
   */
  emitPaintingUpdate(userId, paintingUpdate) {
    if (!this.userSockets) {
      this.init();
    }

    const userSocket = this.userSockets.get(userId);
    if (userSocket) {
      userSocket.emit("painting_update", paintingUpdate);
      console.log(`Emitted painting update to user ${userId}:`, paintingUpdate);
    } else {
      console.log(`User ${userId} not connected via WebSocket`);
    }
  }

  /**
   * Emit painting generation started event
   * @param {number} userId - User ID
   * @param {object} generationData - Generation start data
   */
  emitGenerationStarted(userId, generationData) {
    if (!this.userSockets) {
      this.init();
    }

    const userSocket = this.userSockets.get(userId);
    if (userSocket) {
      userSocket.emit("generation_started", generationData);
      console.log(
        `Emitted generation started to user ${userId}:`,
        generationData
      );
    }
  }

  /**
   * Update painting progress in database and emit to user
   * @param {number} paintingId - Painting ID
   * @param {number} userId - User ID
   * @param {string} status - New status
   * @param {string} stage - Progress stage
   * @param {number} percentage - Progress percentage (0-100)
   * @param {string} errorMessage - Error message if failed
   */
  async updatePaintingProgress(
    paintingId,
    userId,
    status,
    stage,
    percentage = 0,
    errorMessage = null
  ) {
    try {
      // Update database
      const updateQuery = `
        UPDATE paintings 
        SET status = ?, progress_stage = ?, progress_percentage = ?, error_message = ?, updated_at = NOW()
        WHERE id = ?
      `;
      await pool.execute(updateQuery, [
        status,
        stage,
        percentage,
        errorMessage,
        paintingId,
      ]);

      // Get updated painting data
      const [rows] = await pool.execute(
        `
        SELECT p.*, i.summary, i.full_prompt 
        FROM paintings p 
        JOIN ideas i ON p.idea_id = i.id 
        WHERE p.id = ?
      `,
        [paintingId]
      );

      if (rows.length > 0) {
        const painting = rows[0];

        // Emit update to user
        this.emitPaintingUpdate(userId, {
          paintingId: painting.id,
          titleId: painting.title_id,
          ideaId: painting.idea_id,
          status: painting.status,
          stage: painting.progress_stage,
          percentage: painting.progress_percentage,
          imageUrl: painting.image_url,
          errorMessage: painting.error_message,
          summary: painting.summary,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error("Error updating painting progress:", error);
    }
  }

  /**
   * Create placeholder paintings and emit to user
   * @param {number} userId - User ID
   * @param {number} titleId - Title ID
   * @param {array} ideas - Array of generated ideas
   */
  async createPlaceholderPaintings(userId, titleId, ideas) {
    try {
      const placeholders = [];

      for (const idea of ideas) {
        // Create painting entry in database
        const [result] = await pool.execute(
          "INSERT INTO paintings (title_id, idea_id, status, progress_stage, progress_percentage) VALUES (?, ?, ?, ?, ?)",
          [titleId, idea.id, "pending", "pending", 0]
        );

        const paintingId = result.insertId;

        const placeholder = {
          paintingId,
          titleId,
          ideaId: idea.id,
          status: "pending",
          stage: "pending",
          percentage: 0,
          summary: idea.summary,
          timestamp: new Date().toISOString(),
        };

        placeholders.push(placeholder);
      }

      // Emit all placeholders to user
      this.emitGenerationStarted(userId, {
        titleId,
        placeholders,
        totalCount: ideas.length,
      });

      return placeholders;
    } catch (error) {
      console.error("Error creating placeholder paintings:", error);
      throw error;
    }
  }

  /**
   * Get all painting updates for a user (for reconnection)
   * @param {number} userId - User ID
   * @param {number} titleId - Title ID
   */
  async getPaintingUpdates(userId, titleId) {
    try {
      const [rows] = await pool.execute(
        `
        SELECT p.*, i.summary, i.full_prompt, t.user_id
        FROM paintings p 
        JOIN ideas i ON p.idea_id = i.id 
        JOIN titles t ON p.title_id = t.id
        WHERE p.title_id = ? AND t.user_id = ?
        ORDER BY p.created_at ASC
      `,
        [titleId, userId]
      );

      return rows.map((painting) => ({
        paintingId: painting.id,
        titleId: painting.title_id,
        ideaId: painting.idea_id,
        status: painting.status,
        stage: painting.progress_stage,
        percentage: painting.progress_percentage,
        imageUrl: painting.image_url,
        errorMessage: painting.error_message,
        summary: painting.summary,
        timestamp: painting.updated_at || painting.created_at,
      }));
    } catch (error) {
      console.error("Error getting painting updates:", error);
      return [];
    }
  }
}

module.exports = new WebSocketService();
